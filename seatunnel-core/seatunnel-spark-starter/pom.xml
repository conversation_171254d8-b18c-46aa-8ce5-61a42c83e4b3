<?xml version="1.0" encoding="UTF-8"?>
<!--

    Licensed to the Apache Software Foundation (ASF) under one or more
    contributor license agreements.  See the NOTICE file distributed with
    this work for additional information regarding copyright ownership.
    The ASF licenses this file to You under the Apache License, Version 2.0
    (the "License"); you may not use this file except in compliance with
    the License.  You may obtain a copy of the License at

       http://www.apache.org/licenses/LICENSE-2.0

    Unless required by applicable law or agreed to in writing, software
    distributed under the License is distributed on an "AS IS" BASIS,
    WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
    See the License for the specific language governing permissions and
    limitations under the License.

-->
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>org.apache.seatunnel</groupId>
        <artifactId>seatunnel-core</artifactId>
        <version>2.3.4</version>
    </parent>

    <artifactId>seatunnel-spark-starter</artifactId>
    <packaging>pom</packaging>
    <name>SeaTunnel : Core : Spark Starter :</name>

    <modules>
        <module>seatunnel-spark-2-starter</module>
        <module>seatunnel-spark-3-starter</module>
        <module>seatunnel-spark-starter-common</module>
    </modules>

    <properties>
        <docker.repo>seatunnel-spark</docker.repo>
    </properties>

    <dependencies>

        <dependency>
            <groupId>org.apache.seatunnel</groupId>
            <artifactId>seatunnel-core-starter</artifactId>
            <version>${project.version}</version>
        </dependency>

    </dependencies>

    <build>
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-dependency-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-shade-plugin</artifactId>
                <configuration>
                    <artifactSet>
                        <excludes>
                            <!--
                                Spark(2.x) server lib already include:
                                    slf4j-api
                                    log4j
                                    slf4j-log4j12
                                    jul-to-slf4j
                                    jcl-over-slf4j

                                Spark(3.x) server lib already include:
                                    slf4j-api
                                    log4j-api
                                    log4j-core
                                    log4j-slf4j-impl
                                    log4j-1.2-api
                                    jul-to-slf4j
                                    jcl-over-slf4j
                            -->
                            <exclude>org.slf4j:slf4j-api</exclude>
                            <exclude>org.slf4j:slf4j-jdk14</exclude>
                            <exclude>org.slf4j:slf4j-jcl</exclude>
                            <exclude>org.slf4j:slf4j-nop</exclude>
                            <exclude>org.slf4j:slf4j-simple</exclude>
                            <exclude>org.slf4j:slf4j-reload4j</exclude>
                            <exclude>org.slf4j:slf4j-log4j12</exclude>
                            <exclude>org.slf4j:jcl-over-slf4j</exclude>
                            <exclude>org.slf4j:jul-to-slf4j</exclude>
                            <!-- spark2.x use slf4j + log4j1.x -->
                            <exclude>org.slf4j:log4j-over-slf4j</exclude>
                            <exclude>log4j:*</exclude>
                            <exclude>commons-logging:*</exclude>
                            <exclude>ch.qos.logback:*</exclude>
                            <exclude>org.apache.logging.log4j:log4j-api</exclude>
                            <exclude>org.apache.logging.log4j:log4j-core</exclude>
                            <exclude>org.apache.logging.log4j:log4j-slf4j-impl</exclude>
                            <!-- spark3.x use slf4j + log4j2.x -->
                            <exclude>org.apache.logging.log4j:log4j-to-slf4j</exclude>
                            <exclude>org.apache.seatunnel:seatunnel-hadoop3-3.1.4-uber</exclude>
                        </excludes>
                    </artifactSet>
                </configuration>
            </plugin>
        </plugins>
    </build>

</project>

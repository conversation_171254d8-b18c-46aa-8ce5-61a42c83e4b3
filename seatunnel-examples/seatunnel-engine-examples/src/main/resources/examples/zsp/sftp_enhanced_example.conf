env {
   "job.mode"="BATCH"
    "job.name"="SFTP增强连接示例"
}

source {
  SftpFile {
    host = "**************"
    port = 22
    user = root
    password = joyadata
    path = "/opt/zsp/test.txt"
    file_format_type = "text"
    field_delimiter=","
    
    # 新增的连接稳定性配置
    connect_timeout = 30000          # 连接超时时间(毫秒)，默认30秒
    server_alive_interval = 60000    # 心跳间隔(毫秒)，默认60秒
    server_alive_count_max = 3       # 最大心跳失败次数，默认3次
    max_retries = 3                  # 最大重试次数，默认3次
    
    schema= {
        fields {
            name=string
            id=int
        }
    }
  }
}

transform {
}

sink {
  Jdbc {
    url="*******************************************************"
    driver="com.mysql.cj.jdbc.Driver"
    user="root"
    password=joyadata
    database="zsp_test"
    table="test_sftp"
    "connection_check_timeout_sec"="30"
    "batch_size"="1000"
    "is_exactly_once"="false"
    "max_commit_attempts"="3"
    "transaction_timeout_sec"="-1"
    "max_retries"="1"
    "auto_commit"="true"
    "support_upsert_by_query_primary_key_exist"="false"
    "source_table_name"="ZSP_TEST0705_source_1"
    "generate_sink_sql"="true"
    "enable_upsert"="false"
    "pk_strategy"="stop"
    schema_save_mode = "ERROR_WHEN_SCHEMA_NOT_EXIST"
  }
}


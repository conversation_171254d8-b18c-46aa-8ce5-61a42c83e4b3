env {
"job.mode"="BATCH"
"job.name"="10"
}
source {
        Jdbc {
        url="*********************************************************************************************************************************************************************************************************"
        driver="com.mysql.jdbc.Driver"
            schema="seatunnel_source"
            user="root"
            password="joyadata"
        query="SELECT *  FROM `seatunnel_source`.`emp_104` WHERE 1=1 "
        "fetch_size"="1000"
            "result_table_name"="E000001_source_1"
            "parallelism"=1
            "empty_data_strategy"=false
            "table_path"=1
        }


}
transform {
}
sink {
        MergeSftpFile {
        final_name="/usr/local/soft/lihj/test.txt"
        host="**************"
        port="22"
        user="root"
        password="joyadata"
        path="/usr/local/soft/lihj"
            source_table_name="E000001_source_1"
            tmp_path="/usr/local/soft/lihj/temp"
            custom_filename=true
            file_name_expression="test_${transactionId}"
            is_enable_transaction=false
            filename_time_format="yyyy.MM.dd"
            file_format_type="text"
            field_delimiter="\u0001"
            row_delimiter="\n"
            batch_size="100"
            compress_codec="none"
            date_format="yyyy-MM-dd"
            datetime_format="yyyy-MM-dd HH:mm:ss"
            time_format="HH:mm:ss"
            validates="false"
            validate_file="/usr/local/soft/lihj/lihj.ok"
            "empty_data_strategy"=false
        clean_target_folder="false"
            encoding="UTF-8"
        }
}


/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.apache.seatunnel.connectors.seatunnel.argofile.sink.writer;

import org.apache.seatunnel.api.serialization.SerializationSchema;
import org.apache.seatunnel.api.table.type.SeaTunnelDataType;
import org.apache.seatunnel.api.table.type.SeaTunnelRow;
import org.apache.seatunnel.api.table.type.SeaTunnelRowType;
import org.apache.seatunnel.common.exception.CommonError;
import org.apache.seatunnel.common.exception.CommonErrorCodeDeprecated;
import org.apache.seatunnel.common.utils.DateTimeUtils;
import org.apache.seatunnel.common.utils.DateUtils;
import org.apache.seatunnel.common.utils.TimeUtils;
import org.apache.seatunnel.connectors.seatunnel.argofile.config.FileFormat;
import org.apache.seatunnel.connectors.seatunnel.argofile.exception.FileConnectorException;
import org.apache.seatunnel.connectors.seatunnel.argofile.sink.config.FileSinkConfig;
import org.apache.seatunnel.connectors.seatunnel.argofile.sink.util.ArgoUtil;
import org.apache.seatunnel.format.text.TextSerializationSchema;

import org.apache.hadoop.fs.FSDataOutputStream;

import groovy.lang.Tuple2;
import io.airlift.compress.lzo.LzopCodec;
import lombok.NonNull;

import java.io.IOException;
import java.io.OutputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

public class TextWriteStrategy extends AbstractWriteStrategy {
    private final LinkedHashMap<String, FSDataOutputStream> beingWrittenOutputStream;
    private final Map<String, Boolean> isFirstWrite;
    private final String fieldDelimiter;
    private final String rowDelimiter;
    private final DateUtils.Formatter dateFormat;
    private final DateTimeUtils.Formatter dateTimeFormat;
    private final TimeUtils.Formatter timeFormat;
    private final FileFormat fileFormat;
    private final Boolean enableHeaderWriter;
    private SerializationSchema serializationSchema;

    private SeaTunnelRowType argoType;
    private String argoUrl;
    private String argoUser;
    private String argoPassword;
    private String argoSchema;
    private String argoTable;
    private String argoTmpTableName;
    private String argoTmpFilePath;

    public TextWriteStrategy(FileSinkConfig fileSinkConfig) {
        super(fileSinkConfig);
        this.beingWrittenOutputStream = new LinkedHashMap<>();
        this.isFirstWrite = new HashMap<>();
        this.fieldDelimiter = fileSinkConfig.getFieldDelimiter();
        this.rowDelimiter = fileSinkConfig.getRowDelimiter();
        this.dateFormat = fileSinkConfig.getDateFormat();
        this.dateTimeFormat = fileSinkConfig.getDatetimeFormat();
        this.timeFormat = fileSinkConfig.getTimeFormat();
        this.fileFormat = fileSinkConfig.getFileFormat();
        this.enableHeaderWriter = fileSinkConfig.getEnableHeaderWriter();
        this.argoUrl = fileSinkConfig.getArgoUrl();
        this.argoUser = fileSinkConfig.getArgoUser();
        this.argoPassword = fileSinkConfig.getArgoPassword();
        this.argoSchema = fileSinkConfig.getArgoSchema();
        this.argoTable = fileSinkConfig.getArgoTable();
        this.argoTmpTableName = fileSinkConfig.getArgoTmpTableName();
        this.argoTmpFilePath = fileSinkConfig.getPath();
    }

    @Override
    public void setSeaTunnelRowTypeInfo(SeaTunnelRowType seaTunnelRowType) {
        super.setSeaTunnelRowTypeInfo(seaTunnelRowType);
        this.serializationSchema =
                TextSerializationSchema.builder()
                        .seaTunnelRowType(
                                buildSchemaWithRowType(seaTunnelRowType, sinkColumnsIndexInRow))
                        .delimiter(fieldDelimiter)
                        .dateFormatter(dateFormat)
                        .dateTimeFormatter(dateTimeFormat)
                        .timeFormatter(timeFormat)
                        .build();
        argoType = seaTunnelRowType;
    }

    @Override
    public void write(@NonNull SeaTunnelRow seaTunnelRow) {
        super.write(seaTunnelRow);
        String filePath = getOrCreateFilePathBeingWritten(seaTunnelRow);
        FSDataOutputStream fsDataOutputStream = getOrCreateOutputStream(filePath);
        try {
            if (isFirstWrite.get(filePath)) {
                isFirstWrite.put(filePath, false);
            } else {
                fsDataOutputStream.write(rowDelimiter.getBytes());
            }
            fsDataOutputStream.write(
                    serializationSchema.serializeCsv(
                            seaTunnelRow.copy(
                                    sinkColumnsIndexInRow.stream()
                                            .mapToInt(Integer::intValue)
                                            .toArray()),
                            null));

        } catch (IOException e) {
            throw CommonError.fileOperationFailed("TextFile", "write", filePath, e);
        }
    }

    @Override
    public void close() throws IOException {
        Tuple2<String, List<String>> tuple2 =
                getDdl(argoType, argoTmpTableName, argoTmpFilePath, fieldDelimiter);
        log.info("运行完毕了，开始建表吧,sql={}", tuple2.getV1());
        ArgoUtil.executeSql(argoUrl, argoUser, argoPassword, tuple2.getV1());
        log.info("sql执行完毕");
        AtomicInteger count = new AtomicInteger();
        int max = 24 * 60 * 60 * 1000 / 100;
        while (ArgoUtil.executeQuerySql(argoUrl, argoUser, argoPassword, argoTmpTableName) <= 0) {
            try {
                Thread.sleep(100L);
                log.info("等待数据载入完成..");
                if (count.incrementAndGet() > max) {
                    log.error("数据超过30分钟未加载完成，请确认数据量");
                    throw new RuntimeException("数据超过30分钟未加载完成，请确认数据量");
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        String insertSql = getInsertSql(argoTable, argoTmpTableName, tuple2.getV2());
        ArgoUtil.executeSql(argoUrl, argoUser, argoPassword, insertSql);
        log.info("执行完毕，数据写入完毕.{}", insertSql);
        String dropTableSql = "drop table " + argoTmpTableName;
        log.info("开始删除临时表 {} ,sql={}", argoTmpTableName, dropTableSql);
        ArgoUtil.executeSql(argoUrl, argoUser, argoPassword, dropTableSql);
        log.info("临时表删除成功");
        super.close();
    }

    @Override
    public void finishAndCloseFile() {
        beingWrittenOutputStream.forEach(
                (key, value) -> {
                    try {
                        value.flush();
                    } catch (IOException e) {
                        throw new FileConnectorException(
                                CommonErrorCodeDeprecated.FLUSH_DATA_FAILED,
                                String.format("Flush data to this file [%s] failed", key),
                                e);
                    } finally {
                        try {
                            value.close();
                        } catch (IOException e) {
                            log.error("error when close output stream {}", key, e);
                        }
                    }
                    needMoveFiles.put(key, getTargetLocation(key));
                });
        beingWrittenOutputStream.clear();
        isFirstWrite.clear();
    }

    private String getInsertSql(String tableName, String tmpTableName, List<String> columns) {
        String selectColumn = columns.stream().collect(Collectors.joining(", ", "", ""));
        return "insert into " + tableName + " select " + selectColumn + " from " + tmpTableName;
    }

    private Tuple2<String, List<String>> getDdl(
            SeaTunnelRowType rowType, String tmpTable, String tmpFilePath, String fieldDelimiter) {
        List<String> columns = new ArrayList<>();
        String sql = "CREATE EXTERNAL TABLE " + tmpTable + " (";
        for (int i = 0; i < rowType.getFieldNames().length; i++) {
            String fieldName = rowType.getFieldNames()[i];
            columns.add(fieldName);
            SeaTunnelDataType<?> fieldType = rowType.getFieldTypes()[i];
            if (i != 0) {
                sql += ",";
            }
            sql += fieldName + " " + fieldType.getSqlType().toString();
        }
        sql +=
                " )ROW FORMAT DELIMITED FIELDS TERMINATED BY '"
                        + fieldDelimiter
                        + "' LOCATION '"
                        + tmpFilePath
                        + "'";
        return new Tuple2<>(sql, columns);
    }

    private FSDataOutputStream getOrCreateOutputStream(@NonNull String filePath) {
        FSDataOutputStream fsDataOutputStream = beingWrittenOutputStream.get(filePath);
        if (fsDataOutputStream == null) {
            try {
                switch (compressFormat) {
                    case LZO:
                        LzopCodec lzo = new LzopCodec();
                        OutputStream out =
                                lzo.createOutputStream(
                                        hadoopFileSystemProxy.getOutputStream(filePath));
                        fsDataOutputStream = new FSDataOutputStream(out, null);
                        enableWriteHeader(fsDataOutputStream);
                        break;
                    case NONE:
                        fsDataOutputStream = hadoopFileSystemProxy.getOutputStream(filePath);
                        enableWriteHeader(fsDataOutputStream);
                        break;
                    default:
                        log.warn(
                                "Text file does not support this compress type: {}",
                                compressFormat.getCompressCodec());
                        fsDataOutputStream = hadoopFileSystemProxy.getOutputStream(filePath);
                        enableWriteHeader(fsDataOutputStream);
                        break;
                }
                beingWrittenOutputStream.put(filePath, fsDataOutputStream);
                isFirstWrite.put(filePath, true);
            } catch (IOException e) {
                throw CommonError.fileOperationFailed("TextFile", "open", filePath, e);
            }
        }
        return fsDataOutputStream;
    }

    private void enableWriteHeader(FSDataOutputStream fsDataOutputStream) throws IOException {
        if (enableHeaderWriter) {
            fsDataOutputStream.write(
                    String.join(
                                    FileFormat.CSV.equals(fileFormat) ? "," : fieldDelimiter,
                                    seaTunnelRowType.getFieldNames())
                            .getBytes());
            fsDataOutputStream.write(rowDelimiter.getBytes());
        }
    }
}

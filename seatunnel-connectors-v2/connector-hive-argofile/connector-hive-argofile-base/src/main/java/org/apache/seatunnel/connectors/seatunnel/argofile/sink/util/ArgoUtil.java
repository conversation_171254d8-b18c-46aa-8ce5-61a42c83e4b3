package org.apache.seatunnel.connectors.seatunnel.argofile.sink.util;

import lombok.extern.slf4j.Slf4j;

import java.io.File;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/6/5
 */
@Slf4j
public class ArgoUtil {
    public static void executeSql(
            String argoUrl, String argoUser, String argoPassword, String sql) {
        System.setProperty("hive.cli.session.hiveconf", "true");
        final String DRIVER_CLASS = "org.apache.hive.jdbc.HiveDriver";
        try {
            Class.forName(DRIVER_CLASS);
            try (Connection con = DriverManager.getConnection(argoUrl, argoUser, argoPassword);
                    PreparedStatement pstmt = con.prepareStatement(sql)) {
                pstmt.execute();
            } catch (SQLException se) {
                log.error("Error executing SQL: {}", se.getMessage());
            }
        } catch (ClassNotFoundException cnfe) {
            log.error("Driver class not found: {}", cnfe.getMessage());
        }
    }

    public static int executeQuerySql(
            String argoUrl, String argoUser, String argoPassword, String tableName) {
        System.setProperty("hive.cli.session.hiveconf", "true");
        final String DRIVER_CLASS = "org.apache.hive.jdbc.HiveDriver";
        String sql = "select count(1) from " + tableName;
        try {
            Class.forName(DRIVER_CLASS);
            try (Connection con = DriverManager.getConnection(argoUrl, argoUser, argoPassword);
                    PreparedStatement pstmt = con.prepareStatement(sql)) {
                ResultSet resultSet = pstmt.executeQuery();
                if (resultSet.next()) {
                    return resultSet.getInt(1);
                }
            } catch (SQLException se) {
                log.error("Error executing SQL: {}", se);
            }
        } catch (ClassNotFoundException cnfe) {
            log.error("Driver class not found: {}", cnfe);
        }
        return -1;
    }

    public static String getAdbCreateTableSQL(
            Set<String> keySet,
            String schema,
            String tableName,
            String gpfdistAddress,
            String externalTableDelimiter) {
        List<String> fields = keySet.stream().collect(Collectors.toList());
        StringBuilder sb =
                new StringBuilder(
                        "CREATE writable EXTERNAL TABLE " + schema + "." + tableName + " (");
        for (int i = 0; i < fields.size(); i++) {
            sb.append(fields.get(i) + " text");
            if (i < fields.size() - 1) { // 如果不是最后一个元素，则添加逗号
                sb.append(",");
            }
        }
        // sb.append(") LOCATION ('"+gpfdistAddress+"') FORMAT 'CSV' (delimiter ',')");
        sb.append(") LOCATION (");
        String[] addresses = gpfdistAddress.split(",");
        for (int i = 0; i < addresses.length; i++) {
            sb.append('\'').append(addresses[i].trim()).append('\'');
            if (i < addresses.length - 1) { // 如果不是最后一个地址，则添加逗号和单引号
                sb.append(",");
            }
        }

        sb.append(") FORMAT 'CSV' (delimiter '" + externalTableDelimiter + "')");
        return sb.toString();
    }

    public static String getAdbInsertSql(
            String dbSchema, String tmpTableName, String tableName, Set<String> keySet) {
        return "insert into "
                + dbSchema
                + "."
                + tmpTableName
                + " select "
                + String.join(",", keySet)
                + " from "
                + dbSchema
                + "."
                + tableName;
    }

    public static String getAdbDropTable(String dbSchema, String tmpTableName) {
        return "drop EXTERNAL  table " + dbSchema + "." + tmpTableName;
    }

    public static String getGpfdistAddress(String gpfdistAddress, String argoPrefix) {
        List<String> gpfdistAddressList =
                Arrays.stream(gpfdistAddress.split(","))
                        .map(String::trim)
                        .collect(Collectors.toList());

        // 去除 argoPrefix 开头的斜杠
        if (argoPrefix.startsWith("/")) {
            argoPrefix = argoPrefix.substring(1);
        }
        // 处理 gpfdistAddressList 中的每个地址
        String finalArgoPrefix = argoPrefix;
        String gpfdistAddr =
                gpfdistAddressList.stream()
                        .map(
                                addr -> {
                                    // 去除地址结尾的斜杠
                                    if (addr.endsWith("/")) {
                                        addr = addr.substring(0, addr.length() - 1);
                                    }
                                    // 提取端口号
                                    int portIndex = addr.lastIndexOf(':');
                                    String port = addr.substring(portIndex + 1);

                                    // 重新组合地址
                                    return addr + "/" + port + "_" + finalArgoPrefix;
                                })
                        .collect(Collectors.joining(","));
        return gpfdistAddr;
    }

    public static List<String> mkdirFile(
            String filePath, String gpfdistAddress, String argoPrefix) {
        // 使用逗号分隔符解析 gpfdist 地址列表
        List<String> gpfdistAddressList =
                Arrays.stream(gpfdistAddress.split(","))
                        .map(String::trim)
                        .collect(Collectors.toList());

        List<String> filePathList = new ArrayList<>();

        // 去除 filePath 和 argoPrefix 的前导斜杠
        filePath = filePath.endsWith("/") ? filePath.substring(0, filePath.length() - 1) : filePath;
        argoPrefix = argoPrefix.startsWith("/") ? argoPrefix.substring(1) : argoPrefix;

        for (String addr : gpfdistAddressList) {
            // 去除结尾斜杠
            addr = addr.endsWith("/") ? addr.substring(0, addr.length() - 1) : addr;

            // 提取端口号
            String port = addr.substring(addr.lastIndexOf(':') + 1);

            // 生成新的文件路径
            String newFilePath = filePath + "/" + port + "_" + argoPrefix;
            filePathList.add(newFilePath);

            // 尝试创建文件
            try {
                File file = new File(newFilePath);
                log.info("创建文件路径是{}", newFilePath);
                if (!file.exists()) {
                    file.createNewFile();
                    log.info("文件不存在，已创建文件: {}", newFilePath);
                }
            } catch (Exception e) {
                log.error("创建文件失败: {}", newFilePath, e);
            }
        }

        return filePathList;
    }

    public static void checkFile(List<String> filePaths) {
        for (String path : filePaths) {
            File newfile = new File(path);
            while (newfile.length() <= 0) {
                try {
                    log.info("当前文件大小是{}", newfile.length());
                    Thread.sleep(100L);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt(); // 保持中断状态
                    e.printStackTrace();
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
    }
}

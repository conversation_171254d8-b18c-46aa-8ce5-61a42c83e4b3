<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>org.apache.seatunnel</groupId>
        <artifactId>seatunnel-connectors-v2</artifactId>
        <version>2.3.4</version>
    </parent>

    <artifactId>connector-hive-argofile</artifactId>
    <packaging>pom</packaging>
    <description>adb2argo</description>
    <modules>
        <module>connector-hive-argofile-base</module>
        <module>connector-hive-argofile-base-hadoop</module>
        <module>connector-hive-argofile-hadoop</module>
        <module>connector-hive-argofile-local</module>
    </modules>
    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <connector.name>connector.argofile</connector.name>
    </properties>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-shade-plugin</artifactId>
                <executions>
                    <execution>
                        <goals>
                            <goal>shade</goal>
                        </goals>
                        <phase>package</phase>
                        <configuration>
                            <relocations>
                                <relocation>
                                    <pattern>org.apache.avro</pattern>
                                    <!--suppress UnresolvedMavenProperty, this property is added by submodule-->
                                    <shadedPattern>${seatunnel.shade.package}.${connector.name}.org.apache.avro</shadedPattern>
                                </relocation>
                                <relocation>
                                    <pattern>org.apache.orc</pattern>
                                    <shadedPattern>${seatunnel.shade.package}.${connector.name}.org.apache.orc</shadedPattern>
                                </relocation>
                                <relocation>
                                    <pattern>org.apache.parquet</pattern>
                                    <!--suppress UnresolvedMavenProperty -->
                                    <shadedPattern>${seatunnel.shade.package}.${connector.name}.org.apache.parquet</shadedPattern>
                                </relocation>
                                <relocation>
                                    <pattern>shaded.parquet</pattern>
                                    <!--suppress UnresolvedMavenProperty -->
                                    <shadedPattern>${seatunnel.shade.package}.${connector.name}.shaded.parquet</shadedPattern>
                                </relocation>
                            </relocations>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>

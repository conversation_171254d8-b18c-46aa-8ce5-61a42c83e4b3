/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.apache.seatunnel.connectors.seatunnel.adbargo.hdfs.source;

import org.apache.seatunnel.shade.com.fasterxml.jackson.databind.node.ObjectNode;
import org.apache.seatunnel.shade.com.typesafe.config.Config;
import org.apache.seatunnel.shade.com.typesafe.config.ConfigRenderOptions;

import org.apache.seatunnel.api.common.PrepareFailException;
import org.apache.seatunnel.api.common.SeaTunnelAPIErrorCode;
import org.apache.seatunnel.api.table.catalog.CatalogTableUtil;
import org.apache.seatunnel.api.table.catalog.schema.TableSchemaOptions;
import org.apache.seatunnel.api.table.type.SeaTunnelRowType;
import org.apache.seatunnel.common.config.CheckConfigUtil;
import org.apache.seatunnel.common.config.CheckResult;
import org.apache.seatunnel.common.constants.PluginType;
import org.apache.seatunnel.common.exception.CommonErrorCodeDeprecated;
import org.apache.seatunnel.common.utils.JsonUtils;
import org.apache.seatunnel.connectors.seatunnel.adbargo.config.FileFormat;
import org.apache.seatunnel.connectors.seatunnel.adbargo.config.HadoopConf;
import org.apache.seatunnel.connectors.seatunnel.adbargo.exception.FileConnectorErrorCode;
import org.apache.seatunnel.connectors.seatunnel.adbargo.exception.FileConnectorException;
import org.apache.seatunnel.connectors.seatunnel.adbargo.hdfs.source.config.HdfsSourceConfigOptions;
import org.apache.seatunnel.connectors.seatunnel.adbargo.sink.util.ArgoUtil;
import org.apache.seatunnel.connectors.seatunnel.adbargo.source.BaseFileSource;
import org.apache.seatunnel.connectors.seatunnel.adbargo.source.reader.ReadStrategyFactory;

import groovy.lang.Tuple2;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
public abstract class AdbBaseHdfsFileSource extends BaseFileSource {

    @Override
    public void prepare(Config pluginConfig) throws PrepareFailException {
        CheckResult result =
                CheckConfigUtil.checkAllExists(
                        pluginConfig,
                        HdfsSourceConfigOptions.FILE_PATH.key(),
                        HdfsSourceConfigOptions.FILE_FORMAT_TYPE.key(),
                        HdfsSourceConfigOptions.DEFAULT_FS.key());
        if (!result.isSuccess()) {
            throw new FileConnectorException(
                    SeaTunnelAPIErrorCode.CONFIG_VALIDATION_FAILED,
                    String.format(
                            "PluginName: %s, PluginType: %s, Message: %s",
                            getPluginName(), PluginType.SOURCE, result.getMsg()));
        }
        String path = pluginConfig.getString(HdfsSourceConfigOptions.FILE_PATH.key());
        hadoopConf =
                new HadoopConf(pluginConfig.getString(HdfsSourceConfigOptions.DEFAULT_FS.key()));
        if (pluginConfig.hasPath(HdfsSourceConfigOptions.HDFS_SITE_PATH.key())) {
            hadoopConf.setHdfsSitePath(
                    pluginConfig.getString(HdfsSourceConfigOptions.HDFS_SITE_PATH.key()));
        }

        if (pluginConfig.hasPath(HdfsSourceConfigOptions.REMOTE_USER.key())) {
            hadoopConf.setRemoteUser(
                    pluginConfig.getString(HdfsSourceConfigOptions.REMOTE_USER.key()));
        }

        if (pluginConfig.hasPath(HdfsSourceConfigOptions.KERBEROS_PRINCIPAL.key())) {
            hadoopConf.setKerberosPrincipal(
                    pluginConfig.getString(HdfsSourceConfigOptions.KERBEROS_PRINCIPAL.key()));
        }
        if (pluginConfig.hasPath(HdfsSourceConfigOptions.KERBEROS_KEYTAB_PATH.key())) {
            hadoopConf.setKerberosKeytabPath(
                    pluginConfig.getString(HdfsSourceConfigOptions.KERBEROS_KEYTAB_PATH.key()));
        }
        readStrategy =
                ReadStrategyFactory.of(
                        pluginConfig.getString(HdfsSourceConfigOptions.FILE_FORMAT_TYPE.key()));
        readStrategy.setPluginConfig(pluginConfig);
        readStrategy.init(hadoopConf);
        path = initHdfsFile(pluginConfig, hadoopConf);
        try {
            filePaths = readStrategy.getFileNamesByPath(path);
        } catch (IOException e) {
            String errorMsg = String.format("Get file list from this path [%s] failed", path);
            throw new FileConnectorException(
                    FileConnectorErrorCode.FILE_LIST_GET_FAILED, errorMsg, e);
        }

        // support user-defined schema
        FileFormat fileFormat =
                FileFormat.valueOf(
                        pluginConfig
                                .getString(HdfsSourceConfigOptions.FILE_FORMAT_TYPE.key())
                                .toUpperCase());
        // only json text csv type support user-defined schema now
        if (pluginConfig.hasPath(TableSchemaOptions.SCHEMA.key())) {
            switch (fileFormat) {
                case CSV:
                case TEXT:
                case JSON:
                case EXCEL:
                    SeaTunnelRowType userDefinedSchema =
                            CatalogTableUtil.buildWithConfig(pluginConfig).getSeaTunnelRowType();
                    readStrategy.setSeaTunnelRowTypeInfo(userDefinedSchema);
                    rowType = readStrategy.getActualSeaTunnelRowTypeInfo();
                    break;
                case ORC:
                case PARQUET:
                    throw new FileConnectorException(
                            CommonErrorCodeDeprecated.UNSUPPORTED_OPERATION,
                            "SeaTunnel does not support user-defined schema for [parquet, orc] files");
                default:
                    // never got in there
                    throw new FileConnectorException(
                            CommonErrorCodeDeprecated.ILLEGAL_ARGUMENT,
                            "SeaTunnel does not supported this file format");
            }
        } else {
            if (filePaths.isEmpty()) {
                // When the directory is empty, distribute default behavior schema
                rowType = CatalogTableUtil.buildSimpleTextSchema();
                return;
            }
            try {
                rowType = readStrategy.getSeaTunnelRowTypeInfo(filePaths.get(0));
            } catch (FileConnectorException e) {
                String errorMsg =
                        String.format("Get table schema from file [%s] failed", filePaths.get(0));
                throw new FileConnectorException(
                        CommonErrorCodeDeprecated.TABLE_SCHEMA_GET_FAILED, errorMsg, e);
            }
        }
    }

    public static final ConfigRenderOptions CONFIG_RENDER_OPTIONS =
            ConfigRenderOptions.concise().setFormatted(true);

    private String initHdfsFile(Config pluginConfig, HadoopConf hadoopConf) {
        Config schema = pluginConfig.getConfig("schema");
        Config fields = schema.getConfig("fields");
        ObjectNode jsonNodes = JsonUtils.parseObject(fields.root().render(CONFIG_RENDER_OPTIONS));
        List<String> field = new ArrayList<>();
        jsonNodes.fieldNames().forEachRemaining(field::add);
        List<String> fieldTypes = new ArrayList<>();
        for (int i = 0; i < field.size(); i++) {
            fieldTypes.add(fields.getString(field.get(i)));
        }
        String tmpTable = pluginConfig.getString("argo_tmp_table_name");
        String tmpFilePath = pluginConfig.getString("path");
        String argoUrl = pluginConfig.getString("argo_url");
        String argoUser = pluginConfig.getString("argo_user");
        String argoPassword = pluginConfig.getString("argo_password");
        // String argoSchema = pluginConfig.getString("argo_schema");
        String argoTable = pluginConfig.getString("argo_table");
        Tuple2<String, List<String>> tuple2 = getDdl(field, fieldTypes, tmpTable, tmpFilePath);
        log.info("建临时表，表名称是{}，建表语句是{}", tmpTable, tuple2.getV1());
        ArgoUtil.executeSql(argoUrl, argoUser, argoPassword, tuple2.getV1());
        log.info("建临时表完毕,开始插入数据到临时表");
        String insertSql = getInsertSql(argoTable, tmpTable, tuple2.getV2());
        log.info("生成的插入语句是{}", insertSql);
        ArgoUtil.executeSql(argoUrl, argoUser, argoPassword, insertSql);
        log.info("执行完毕，数据写入完毕");
        return tmpFilePath;
        /*for (int i = 0; i < fields.root().entrySet(); i++) {
            String fieldName = fields.getString(i + ".name");
            String fieldType = fields.getString(i + ".type");
            String fieldComment = fields.getString(i + ".comment");
            String fieldNullable = fields.getString(i + ".nullable");
            String fieldDefaultValue = fields.getString(i + ".default_value");
            String fieldPrecision = fields.getString(i + ".precision");
            String fieldScale = fields.getString(i + ".scale");
        }*/
        /*argoUrl = hadoopConf.getString("argo_url");
        argoUser = hadoopConf.getString("argo_user");
        argoPassword = pluginConfig.getString("argo_password");
        argoSchema = pluginConfig.getString("argo_schema");
        argoTable = pluginConfig.getString("argo_table");
        argoTmpTableName = pluginConfig.getString("argo_tmp_table_name");
        argoTmpFilePath = pluginConfig.getString("argo_tmp_file_path");*/
    }

    private Tuple2<String, List<String>> getDdl(
            List<String> fieldNames, List<String> fieldTypes, String tmpTable, String tmpFilePath) {
        String sql = "CREATE EXTERNAL TABLE " + tmpTable + " (";
        for (int i = 0; i < fieldNames.size(); i++) {
            String fieldName = fieldNames.get(i);
            if (i != 0) {
                sql += ",";
            }
            sql += fieldName + " " + fieldTypes.get(i);
        }
        sql += " )ROW FORMAT DELIMITED FIELDS TERMINATED BY ',' LOCATION '" + tmpFilePath + "'";
        return new Tuple2<>(sql, fieldNames);
    }

    private String getInsertSql(String tableName, String tmpTableName, List<String> columns) {
        String selectColumn = columns.stream().collect(Collectors.joining(", ", "", ""));
        return "insert into " + tmpTableName + " select " + selectColumn + " from " + tableName;
    }
}

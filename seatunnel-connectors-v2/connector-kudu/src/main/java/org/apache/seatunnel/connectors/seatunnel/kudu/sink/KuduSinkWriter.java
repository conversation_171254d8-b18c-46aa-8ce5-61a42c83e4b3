/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements.  See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License.  You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.apache.seatunnel.connectors.seatunnel.kudu.sink;

import org.apache.seatunnel.api.sink.SinkWriter;
import org.apache.seatunnel.api.sink.SupportMultiTableSinkWriter;
import org.apache.seatunnel.api.table.type.SeaTunnelRow;
import org.apache.seatunnel.api.table.type.SeaTunnelRowType;
import org.apache.seatunnel.connectors.seatunnel.kudu.config.KuduSinkConfig;
import org.apache.seatunnel.connectors.seatunnel.kudu.kuduclient.KuduOutputFormat;
import org.apache.seatunnel.connectors.seatunnel.kudu.state.KuduCommitInfo;
import org.apache.seatunnel.connectors.seatunnel.kudu.state.KuduSinkState;

import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.util.Arrays;
import java.util.Optional;

@Slf4j
public class KuduSinkWriter
        implements SinkWriter<SeaTunnelRow, KuduCommitInfo, KuduSinkState>,
                SupportMultiTableSinkWriter<Void> {

    private SeaTunnelRowType seaTunnelRowType;
    private KuduOutputFormat fileWriter;

    public KuduSinkWriter(
            @NonNull SeaTunnelRowType seaTunnelRowType, @NonNull KuduSinkConfig kuduSinkConfig) {
        this.seaTunnelRowType = seaTunnelRowType;
        fileWriter = new KuduOutputFormat(kuduSinkConfig, seaTunnelRowType);
    }

    @Override
    public void write(SeaTunnelRow element) throws IOException {
        int bytesSize = element.getBytesSize();
        boolean flag = Arrays.stream(element.getFields()).allMatch(field -> field == null);
        if (bytesSize == 0 && flag) {
            return;
        }
        fileWriter.write(element);
    }

    @Override
    public Optional<KuduCommitInfo> prepareCommit() throws IOException {
        fileWriter.flush();
        return Optional.empty();
    }

    @Override
    public void abortPrepare() {}

    @Override
    public void close() throws IOException {
        fileWriter.closeOutputFormat();
    }
}
